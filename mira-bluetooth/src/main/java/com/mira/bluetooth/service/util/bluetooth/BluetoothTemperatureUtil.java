package com.mira.bluetooth.service.util.bluetooth;

import com.mira.bluetooth.dto.BluetoothDataDTO;
import com.mira.bluetooth.dal.entity.AppDataTemperatureEntity;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.util.UnitConvertUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import org.apache.http.util.TextUtils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

/**
 * 解析蓝牙温度计工具类
 * <p>
 * 数据有效性判断条件:
 * 命令头为固定值 FEFD，命令尾为固定值0D0A，且长度=16；
 * <p>
 * 数据解析结果校验例子：
 * 例1：“FEFD1A1101510D0A“  =>FEFD 1A 11 0151 0D0A
 * 1A => 单位：°C(摄氏度); 11 => 口腔; 0151 => 温度：33.7°C (92.6°F)
 * <p>
 * 例2：“FEFD154403BF0D0A“  =>FEFD 15 44 03BF 0D0A
 * 15 => 单位：°F(华氏度); 44 => 腋窝; 03BF => 温度：95.9°F (35.5°C)
 * <p>
 * 例3：“FEFD008100000D0A“  =>FEFD 00 81 0000 0D0A
 * 810000 => 测温度过高,>= 42.00°C (107.6°F),仪器显示HI
 * <p>
 * 例4：“FEFD008200000D0A“  =>FEFD 00 82 0000 0D0A
 * 820000 => 测温度过低, <32.00°C (90.0°F),仪器显示LO
 */
public class BluetoothTemperatureUtil {
    public static AppDataTemperatureEntity parseTemperature(BluetoothDataDTO inBluetoothData, Long userId, String timeZone) {
        Long completeTime = inBluetoothData.getCompleteTimeStamp();
        String data = inBluetoothData.getBluetoothData();

        AppDataTemperatureEntity dataTemperatureEntity = new AppDataTemperatureEntity();
        UpdateEntityTimeUtil.setBaseEntityTime(timeZone, dataTemperatureEntity);
        String completeTimeStr = ZoneDateUtil.format(timeZone, completeTime, DatePatternConst.DATE_TIME_PATTERN);
        dataTemperatureEntity.setCompleteTime(completeTimeStr);
        dataTemperatureEntity.setCompleteTimestamp(completeTime);
        dataTemperatureEntity.setUserId(userId);
        dataTemperatureEntity.setRawResultData(data);

        String error = "00";
        String modeError = "00";
        dataTemperatureEntity.setError(error);
        dataTemperatureEntity.setModeError(modeError);
        if (TextUtils.isEmpty(data) || data.length() != 16) {
            //数据异常
            error = "01";
            dataTemperatureEntity.setError(error);
            return dataTemperatureEntity;
        }
        if (!data.startsWith("FEFD") || !data.endsWith("0D0A")) {
            //过滤掉非FEFD开头、非0D0A结尾的数据
            error = "02";
            dataTemperatureEntity.setError(error);
            return dataTemperatureEntity;
        }
        //截取Byte3，单位，1A为°C ；15为°F
        String unitFlag = data.substring(4, 6);
        Integer unit = 0;
        if (!("1A".equals(unitFlag) || "15".equals(unitFlag))) {
            //其它异常错误信息
            error = "03";
            dataTemperatureEntity.setError(error);
        } else if ("1A".equals(unitFlag)) {
            unit = 1;
        } else if ("15".equals(unitFlag)) {
            unit = 2;
        }
        dataTemperatureEntity.setUnit(unit);
        dataTemperatureEntity.setModeError(getModeError(data));
        dataTemperatureEntity.setMode(getMode(data));

        //截取温度高位 Byte5
        String th = data.substring(8, 10);
        //截取温度低位 Byte6
        String tl = data.substring(10, 12);
        //十六进制转十进制
        int thValue = Integer.parseInt(th, 16);
        int tlValue = Integer.parseInt(tl, 16);

        BigDecimal temperature = getTemperature(thValue, tlValue, unit);
        dataTemperatureEntity.setTemperature(temperature);
        return dataTemperatureEntity;
    }

    /**
     * 如果是2位小数，温度值永远为°C，如果unit=2，需要转为°F存储（温度计的原因）
     *
     * @param thValue
     * @param tlValue
     * @param unit
     * @return
     */
    private static BigDecimal getTemperature(int thValue, int tlValue, int unit) {
        //先以1位小数计算
        float temperatureF = (thValue * 256 + tlValue) / 10f;
        BigDecimal temperature = new BigDecimal(Float.toString(temperatureF));

        BigDecimal tempC = temperature;
        if (unit == 2) {
            tempC = UnitConvertUtil.getUnitConvert("temp", TempUnitEnum.F.getValue(), temperature);
        }
        // 如果温度不合理，转为2位小数计算
        if (tempC.floatValue() > 50) {
            temperatureF = (thValue * 256 + tlValue) / 100f;
            temperature = new BigDecimal(Float.toString(temperatureF));
            if (unit == 2) {
                temperature = temperature.multiply(new BigDecimal("1.8")).add(new BigDecimal("32"));
            }
        }
        //这里按照温度计显示逻辑，去尾（非四舍五入）
        BigDecimal rounded = temperature.round(new MathContext(4, RoundingMode.DOWN));
        return rounded;
    }


    /**
     * 截取Byte4测量模式，界面显示可用
     * 11 : 口腔 Oral Detection;
     * 44 : 腋窝 Armpit Detection;
     * 22 : 肛门 Anal Detection;
     */
    private static Integer getMode(String data) {
        Integer mode = 0;
        String modeFlag = data.substring(6, 8);
        if ("11".equals(modeFlag)) {
            mode = 1;
        } else if ("44".equals(modeFlag)) {
            mode = 2;
        } else if ("22".equals(modeFlag)) {
            mode = 3;
        }
        return mode;
    }

    private static String getModeError(String data) {
        String modeError = "00";
        //截取测量异常信息，Byte4 + Byte5 + Byte6
        String modeFlagError = data.substring(6, 12);

        if (modeFlagError.startsWith("81")) {
            //量测温度过高, >= 42.00°C (107.6°F),仪器显示HI
            modeError = "01";
        } else if (modeFlagError.startsWith("82")) {
            //量测温度过低, <32.00°C (90.0°F),仪器显示LO
            modeError = "02";

        } else if (modeFlagError.startsWith("83")) {
            //表示电池低电压, ##这里协议文档中值为83，源码中值为86，待确认？？？！！！##
            modeError = "03";
        } else if (modeFlagError.startsWith("84")) {
            //表示ERR错误
            modeError = "04";
        }
        return modeError;
    }
}
