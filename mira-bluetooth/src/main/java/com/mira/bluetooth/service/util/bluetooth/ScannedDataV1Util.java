package com.mira.bluetooth.service.util.bluetooth;

import com.mira.bluetooth.dal.entity.AppScannedDataUploadEntity;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.ZoneDateUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@Slf4j
public class ScannedDataV1Util {
    public static AppScannedDataUploadEntity parseData(String data, Long userId, String timeZone) {
        AppScannedDataUploadEntity scannedDataUploadEntity = new AppScannedDataUploadEntity();
        scannedDataUploadEntity.setUserId(userId);
        //resultVersionFormat
        String resultVersionFormat = data.substring(0, 2);
        // 完成时间
        long completeTime = Long.parseLong(data.substring(2, 10), 16) * 1000;
        String completeTimeStr = ZoneDateUtil.format(timeZone, completeTime, DatePatternConst.DATE_TIME_PATTERN);
        //analyzerSN
        scannedDataUploadEntity.setSn(SnUtil.getSn(data.substring(26, 42)));
        //analyzerNumber
        scannedDataUploadEntity.setAnalyzerNumber(decodeHEX(data.substring(42, 46)));
        //scanData
        String scanData = data.substring(46, 4046);
        float [] parsedScanDatas = parseScanData(scanData);
        String scanDataArray = JsonUtil.toJson(parsedScanDatas);

        scannedDataUploadEntity.setResultVersionFormat(resultVersionFormat);
        scannedDataUploadEntity.setCompleteTime(completeTimeStr);
        scannedDataUploadEntity.setCompleteTimestamp(completeTime);
        //从下一次发版开始data存放原始数据
        scannedDataUploadEntity.setScanData(data);
        scannedDataUploadEntity.setScanDataArray(scanDataArray);
        return scannedDataUploadEntity;
    }




//    /**
//     * 解析长度为 4000 的扫描数据字符串，将每 8 个字符解析为一个 float 数值。
//     *
//     * @param scanData 长度为 4000 的扫描数据字符串，内容为16进制字符
//     * @return float 数组，每个元素对应 scanData 中 8 位转换后的 float 数值
//     */
//    private static float[] parseScanData(String scanData) {
//        // 计算应有的 float 数量（4000/8 = 500）
//        int numValues = scanData.length() / 8;
//        float[] result = new float[numValues];
//
//        for (int i = 0; i < numValues; i++) {
//            // 取出第 i 个 8 位子串
//            String hexSegment = scanData.substring(i * 8, (i + 1) * 8);
//            // 使用 decodeHEX 方法转换得到 float 数值
//            result[i] = decodeHEX(hexSegment);
//        }
//
//        return result;
//    }


    /**
     * 解析长度为 4000 的扫描数据字符串，将每 8 个字符解析为一个 float 数值（小端模式）。
     *
     * @param scanData 长度为 4000 的扫描数据字符串，内容为16进制字符
     * @return float 数组，每个元素对应 scanData 中 8 位转换后的 float 数值
     */
    public static float[] parseScanData(String scanData) {
        // 计算应有的 float 数量（4000/8 = 500）
        int numValues = scanData.length() / 8;
        float[] result = new float[numValues];

        for (int i = 0; i < numValues; i++) {
            // 获取当前 8 位小端模式的十六进制字符串
            String hexSegment = scanData.substring(i * 8, (i + 1) * 8);

            // 先转换为小端表示的整数
            int leIntBits = decodeHEXLittleEndian(hexSegment);

            // 再转换为 float 数值
            result[i] = Float.intBitsToFloat(leIntBits);
        }
        return result;
    }

    /**
     * 将 8 位十六进制字符串（表示 IEEE 754 float）解析为 int（小端格式）
     *
     * 示例：
     * - 输入 hex: "0000803F"（小端存储的 1.0）
     * - 重新排列字节顺序："3F800000"
     * - 解析为 int: `0x3F800000`（大端值）
     * - 结果转换为 float: `1.0`
     *
     * @param hex 8 位十六进制字符串（小端格式）
     * @return 小端模式下的 IEEE 754 float 转换后的 int bits
     */
    public static int decodeHEXLittleEndian(String hex) {
        // 先转换为字节数组
        byte[] bytes = new byte[4];
        for (int i = 0; i < 4; i++) {
            // 每次取两个字符转换为 1 个字节
            String byteHex = hex.substring(i * 2, i * 2 + 2);
            bytes[3 - i] = (byte) Integer.parseInt(byteHex, 16); // 翻转字节顺序，改为小端
        }

        // 使用 ByteBuffer 以小端模式转换
        return ByteBuffer.wrap(bytes).order(ByteOrder.BIG_ENDIAN).getInt();
    }



    /**
     * 将16进制的字符串转10进制的数字
     */
    public static int decodeHEX(String hexs) {
        BigInteger bigint = new BigInteger(hexs, 16);
        int numb = bigint.intValue();
        return numb;
    }

}
