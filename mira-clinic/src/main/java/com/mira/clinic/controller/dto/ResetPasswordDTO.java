package com.mira.clinic.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ApiModel("忘记密码请求参数")
public class ResetPasswordDTO {
    @ApiModelProperty("hash")
    @NotBlank(message = "hash can not be empty.")
    private String hash;

    @ApiModelProperty("新密码")
    @NotBlank(message = "Don’t forget your new password!")
    private String pw;
}
