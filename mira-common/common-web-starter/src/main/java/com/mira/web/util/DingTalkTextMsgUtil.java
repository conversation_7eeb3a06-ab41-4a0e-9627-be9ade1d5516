package com.mira.web.util;

import cn.hutool.json.JSONUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.LocalDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 钉钉消息通知工具类
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-01-16
 **/
@Slf4j
@Component
public class DingTalkTextMsgUtil {
    private static String dingToken;

    @Value("${dingtoken:''}")
    public void setDingToken(String token) {
        DingTalkTextMsgUtil.dingToken = token;
    }

    private static final String DING_TALK_BASE_URL = "https://oapi.dingtalk.com/robot/send?access_token=";

    /**
     * 发送钉钉消息
     *
     * @param userId  用户id
     * @param url     url
     * @param content 钉钉机器人消息内容
     * @param isAtAll 是否需要@所有人
     */
    public static void sendDingTalk(String userId, String url, String content,
                                    String traceId, boolean isAtAll) {
        if (StringUtils.isBlank(dingToken)) {
            return;
        }
        String textMsg = "{\n" +
                "    \"title\": \"生产报错\",\n" +
                "    \"url\": " + url + ",\n" +
                "    \"content\": \"" + content + "\",\n" +
                "    \"user\": " + userId + ",\n" +
                "    \"traceId\": " + traceId + ",\n" +
                "    \"time\": " + LocalDateUtil.format(LocalDateTime.now(), DatePatternConst.DATE_TIME_PATTERN) + "\n" +
                "}";

        String contentPref = "mira:";
        String reqStr = buildTextReq(contentPref + textMsg, isAtAll);
        try {
            OkHttpUtil.post(DING_TALK_BASE_URL.concat(dingToken), reqStr);
        } catch (IOException e) {
            log.error("发送钉钉消息失败", e);
        }
    }

    /**
     * 组装请求报文
     */
    private static String buildTextReq(String content, boolean isAtAll) {
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content", content);
        Map<String, Object> atMap = new HashMap<>();
        atMap.put("isAtAll", isAtAll);
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("msgtype", "text");
        reqMap.put("text", contentMap);
        reqMap.put("at", atMap);
        return JSONUtil.toJsonStr(reqMap);
    }
}


