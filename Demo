{"tenant": "mira", "userId": "685101c802776c0002f2661f", "message": "this is user's data", "context": {"biomarker": ["12", "16"], "template": {"user_mode": "2", "age": 26, "user_conditions": ["2", "0", "3"], "len_cycle_0": 30, "today": "2025-06-18", "hormone_data": [{"test_time": "2023-06-27 13:18:06", "test_results": {"wand_type": 16, "value1": 8.88, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221827, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-06-27 13:49:11", "test_results": {"wand_type": 12, "value1": 2.75, "value2": 0.99, "value3": 62.3, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221826, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-06-28 15:36:33", "test_results": {"wand_type": 16, "value1": 4.76, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221825, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-06-28 16:38:13", "test_results": {"wand_type": 12, "value1": 1.36, "value2": 0.99, "value3": 40.39, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221824, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-06-30 12:49:48", "test_results": {"wand_type": 16, "value1": 7.29, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221823, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-06-30 13:12:21", "test_results": {"wand_type": 12, "value1": 3.61, "value2": 1.65, "value3": 106.32, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221822, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-01 13:38:01", "test_results": {"wand_type": 16, "value1": 11.57, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221821, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-01 14:00:52", "test_results": {"wand_type": 12, "value1": 3.73, "value2": 2, "value3": 165.88, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221820, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-02 18:13:56", "test_results": {"wand_type": 16, "value1": 9.42, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221819, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-02 19:08:11", "test_results": {"wand_type": 12, "value1": 2.61, "value2": 0.99, "value3": 67.42, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221818, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-03 12:58:03", "test_results": {"wand_type": 16, "value1": 6.17, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221817, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-03 15:31:49", "test_results": {"wand_type": 12, "value1": 2.13, "value2": 0.99, "value3": 38.03, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221816, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-04 13:44:43", "test_results": {"wand_type": 16, "value1": 13.82, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221815, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-04 14:06:42", "test_results": {"wand_type": 12, "value1": 3.41, "value2": 2.39, "value3": 133.36, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221814, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-05 18:25:13", "test_results": {"wand_type": 16, "value1": 11.54, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221813, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-05 18:57:06", "test_results": {"wand_type": 12, "value1": 3.6, "value2": 0.99, "value3": 129.13, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221812, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-06 14:39:19", "test_results": {"wand_type": 16, "value1": 7.03, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221811, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-06 15:00:29", "test_results": {"wand_type": 12, "value1": 2.94, "value2": 1.88, "value3": 148.25, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221810, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-07 13:02:05", "test_results": {"wand_type": 16, "value1": 8.17, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221809, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-07 13:25:40", "test_results": {"wand_type": 12, "value1": 3.29, "value2": 1.42, "value3": 150.84, "value4": null, "Ecode": null}, "wandBatch3": "2023031206", "id": 10221808, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-09 15:08:06", "test_results": {"wand_type": 12, "value1": 14.62, "value2": 5.73, "value3": 396.94, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221807, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-10 13:52:11", "test_results": {"wand_type": 12, "value1": 15.81, "value2": 3.62, "value3": 558.22, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221806, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-11 14:16:58", "test_results": {"wand_type": 12, "value1": 7.94, "value2": 17.31, "value3": 395.52, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221805, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-12 14:00:37", "test_results": {"wand_type": 12, "value1": 2.91, "value2": 3.7, "value3": 69.31, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221804, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-20 13:10:09", "test_results": {"wand_type": 12, "value1": 2.05, "value2": 6.54, "value3": 182.5, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221803, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-30 15:17:37", "test_results": {"wand_type": 16, "value1": 3.98, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221802, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-07-30 15:57:46", "test_results": {"wand_type": 12, "value1": 2.12, "value2": 0.99, "value3": 43.34, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221801, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-01 13:58:10", "test_results": {"wand_type": 16, "value1": 7.04, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221800, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-01 14:20:17", "test_results": {"wand_type": 12, "value1": 2.85, "value2": 1.65, "value3": 109.33, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221799, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-03 16:50:32", "test_results": {"wand_type": 16, "value1": 2.28, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221798, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-03 17:12:24", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 3.21, "value3": 121.17, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221797, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-04 12:05:58", "test_results": {"wand_type": 16, "value1": 7.49, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221796, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-04 13:06:38", "test_results": {"wand_type": 12, "value1": 3.38, "value2": 1.2, "value3": 155.81, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221795, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-05 16:09:54", "test_results": {"wand_type": 16, "value1": 4.68, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221794, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-05 16:40:31", "test_results": {"wand_type": 12, "value1": 2.77, "value2": 0.99, "value3": 122.77, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221793, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-06 14:43:29", "test_results": {"wand_type": 16, "value1": 6.65, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221792, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-06 15:25:50", "test_results": {"wand_type": 12, "value1": 7.13, "value2": 0.99, "value3": 138.76, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221791, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-07 11:53:30", "test_results": {"wand_type": 16, "value1": 11.49, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221790, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-07 12:14:47", "test_results": {"wand_type": 12, "value1": 15.23, "value2": 0.99, "value3": 153.3, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221789, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-08 11:15:16", "test_results": {"wand_type": 16, "value1": 25, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221788, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-08 11:40:15", "test_results": {"wand_type": 12, "value1": 16.02, "value2": 5.49, "value3": 272.32, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221787, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-09 13:12:02", "test_results": {"wand_type": 16, "value1": 2.42, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221786, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-09 13:36:33", "test_results": {"wand_type": 12, "value1": 1.01, "value2": 4.93, "value3": 134.81, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221785, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-10 17:01:52", "test_results": {"wand_type": 16, "value1": 1.75, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221784, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-10 17:24:28", "test_results": {"wand_type": 12, "value1": 1.28, "value2": 1.3, "value3": 75.15, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221783, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-11 18:04:22", "test_results": {"wand_type": 16, "value1": 6.34, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221782, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-11 22:55:09", "test_results": {"wand_type": 12, "value1": 4.13, "value2": 30.01, "value3": 513.53, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221781, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-12 22:10:00", "test_results": {"wand_type": 16, "value1": 3.19, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221780, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-12 22:58:40", "test_results": {"wand_type": 12, "value1": 3.05, "value2": 30.01, "value3": 333.71, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221779, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-13 15:38:11", "test_results": {"wand_type": 16, "value1": 4.12, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221778, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-13 17:42:40", "test_results": {"wand_type": 12, "value1": 2.22, "value2": 12.86, "value3": 301.63, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221777, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-14 12:45:56", "test_results": {"wand_type": 12, "value1": 1.54, "value2": 4.53, "value3": 220.08, "value4": null, "Ecode": null}, "wandBatch3": "2023041201", "id": 10221776, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-15 19:38:39", "test_results": {"wand_type": 12, "value1": 5.61, "value2": 15.24, "value3": 311.41, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221775, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-16 15:40:20", "test_results": {"wand_type": 12, "value1": 1.9, "value2": 8.38, "value3": 234.05, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221774, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-17 17:38:51", "test_results": {"wand_type": 12, "value1": 4.32, "value2": 30.01, "value3": 412.73, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221773, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-18 11:26:30", "test_results": {"wand_type": 12, "value1": 4.52, "value2": 27, "value3": 226.07, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221772, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-19 16:07:08", "test_results": {"wand_type": 12, "value1": 3.85, "value2": 20.62, "value3": 269.99, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221771, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-20 15:34:10", "test_results": {"wand_type": 12, "value1": 3.19, "value2": 3.15, "value3": 120.61, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221770, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:15:19", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221769, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:15:43", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221768, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:15:52", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221767, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:16:00", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221766, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:16:10", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221765, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:17:41", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221764, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:18:03", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221763, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:18:10", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221762, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 13:41:22", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221761, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-21 20:17:22", "test_results": {"wand_type": 12, "value1": 5, "value2": 2.72, "value3": 139.06, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221760, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-25 20:39:10", "test_results": {"wand_type": 16, "value1": 9.75, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221759, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-25 21:57:18", "test_results": {"wand_type": 12, "value1": 3.59, "value2": 1.1, "value3": 61.83, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221758, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-29 01:20:02", "test_results": {"wand_type": 16, "value1": 5.67, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221757, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-29 01:58:14", "test_results": {"wand_type": 12, "value1": 4.21, "value2": 0.99, "value3": 53.3, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221756, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-29 20:13:38", "test_results": {"wand_type": 16, "value1": 3.89, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221755, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-29 20:35:30", "test_results": {"wand_type": 12, "value1": 2.95, "value2": 1.12, "value3": 83.34, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221754, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-31 00:19:55", "test_results": {"wand_type": 16, "value1": 6.1, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221753, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-31 01:07:58", "test_results": {"wand_type": 12, "value1": 3.89, "value2": 11.87, "value3": 414.24, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221752, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-31 18:02:35", "test_results": {"wand_type": 16, "value1": 5.71, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221751, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-08-31 18:37:18", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 4.43, "value3": 219.4, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221750, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-01 15:37:23", "test_results": {"wand_type": 16, "value1": 4.86, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221749, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-01 17:22:58", "test_results": {"wand_type": 12, "value1": 4.81, "value2": 1.64, "value3": 113.24, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221748, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-02 23:01:31", "test_results": {"wand_type": 16, "value1": 9.08, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023041602", "id": 10221747, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-02 23:22:44", "test_results": {"wand_type": 12, "value1": 11.95, "value2": 2.14, "value3": 364.15, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221746, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-03 21:33:30", "test_results": {"wand_type": 16, "value1": 54.45, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221745, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-03 22:02:06", "test_results": {"wand_type": 12, "value1": 49.09, "value2": 6.7, "value3": 640.01, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221744, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-04 23:02:17", "test_results": {"wand_type": 16, "value1": 18.58, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221743, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-04 23:43:12", "test_results": {"wand_type": 12, "value1": 9.88, "value2": 5.91, "value3": 640.01, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221742, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-05 19:28:31", "test_results": {"wand_type": 16, "value1": 8.39, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221741, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-05 19:57:45", "test_results": {"wand_type": 12, "value1": 2.58, "value2": 11.48, "value3": 277.8, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221740, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-06 16:00:18", "test_results": {"wand_type": 16, "value1": 3.4, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221739, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-06 18:11:46", "test_results": {"wand_type": 12, "value1": 3.41, "value2": 21.15, "value3": 306.02, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221738, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-07 19:47:52", "test_results": {"wand_type": 16, "value1": 2.52, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221737, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-07 20:32:23", "test_results": {"wand_type": 12, "value1": 3.53, "value2": 25.21, "value3": 349.1, "value4": null, "Ecode": null}, "wandBatch3": "2023051202", "id": 10221736, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-11 14:40:07", "test_results": {"wand_type": 12, "value1": 2.92, "value2": 9.65, "value3": 394.69, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221735, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-13 18:39:06", "test_results": {"wand_type": 12, "value1": 2.83, "value2": 6.61, "value3": 223.63, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221734, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-14 18:38:57", "test_results": {"wand_type": 12, "value1": 2.06, "value2": 28.23, "value3": 640.01, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221733, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-16 19:26:28", "test_results": {"wand_type": 12, "value1": 3.51, "value2": 19.56, "value3": 640.01, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221732, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-17 16:20:00", "test_results": {"wand_type": 12, "value1": 2.46, "value2": 2.55, "value3": 101.24, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221731, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-26 15:02:55", "test_results": {"wand_type": 16, "value1": 12.27, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221730, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-26 18:45:39", "test_results": {"wand_type": 12, "value1": 2.37, "value2": 2.57, "value3": 168.65, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221729, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-27 16:45:37", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221728, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-27 16:45:45", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221727, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-27 16:45:54", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221726, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-27 16:46:14", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221725, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-27 17:02:49", "test_results": {"wand_type": 16, "value1": 9.4, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221724, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-27 18:33:44", "test_results": {"wand_type": 12, "value1": 2.45, "value2": 7.98, "value3": 330.41, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221723, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-28 17:06:05", "test_results": {"wand_type": 16, "value1": 5.5, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221722, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-28 17:52:01", "test_results": {"wand_type": 12, "value1": 2.91, "value2": 1, "value3": 118.36, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221721, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-29 15:17:12", "test_results": {"wand_type": 16, "value1": 3.8, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221720, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-29 15:40:07", "test_results": {"wand_type": 12, "value1": 3.05, "value2": 0.99, "value3": 64.95, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221719, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-30 10:41:53", "test_results": {"wand_type": 16, "value1": 14.17, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221718, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-09-30 11:08:04", "test_results": {"wand_type": 12, "value1": 5.18, "value2": 6.81, "value3": 491.15, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221717, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-01 22:27:57", "test_results": {"wand_type": 16, "value1": 1, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221716, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-01 23:02:03", "test_results": {"wand_type": 12, "value1": 1.58, "value2": 0.99, "value3": 31.2, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221715, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-02 19:03:32", "test_results": {"wand_type": 16, "value1": 5.38, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221714, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-02 20:32:19", "test_results": {"wand_type": 12, "value1": 4.76, "value2": 1.59, "value3": 143.23, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221713, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-03 15:34:29", "test_results": {"wand_type": 16, "value1": 7.29, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221712, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-03 16:07:23", "test_results": {"wand_type": 12, "value1": 6.4, "value2": 0.99, "value3": 120.37, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221711, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-04 20:48:24", "test_results": {"wand_type": 12, "value1": 11.85, "value2": 2.92, "value3": 348.84, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221710, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-06 16:24:54", "test_results": {"wand_type": 12, "value1": 6.99, "value2": 0.99, "value3": 81.85, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221709, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-11 17:19:20", "test_results": {"wand_type": 12, "value1": 2.33, "value2": 2.6, "value3": 86.84, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221708, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-17 17:16:21", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 1.99, "value3": 165.62, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221707, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-25 20:30:41", "test_results": {"wand_type": 16, "value1": 9.51, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221706, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-25 22:19:46", "test_results": {"wand_type": 12, "value1": 2.76, "value2": 3.49, "value3": 158.63, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221705, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-26 19:22:29", "test_results": {"wand_type": 16, "value1": 6.07, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221704, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-26 20:22:32", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 1.78, "value3": 175.91, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221703, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-27 18:43:45", "test_results": {"wand_type": 16, "value1": 10.46, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221702, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-27 19:05:40", "test_results": {"wand_type": 12, "value1": 3.87, "value2": 1.43, "value3": 113.17, "value4": null, "Ecode": null}, "wandBatch3": "2023071202", "id": 10221701, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-29 22:43:38", "test_results": {"wand_type": 16, "value1": 8.74, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221700, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-29 23:22:00", "test_results": {"wand_type": 12, "value1": 3.42, "value2": 3.45, "value3": 149.96, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221699, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-30 17:34:57", "test_results": {"wand_type": 16, "value1": 6.82, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221698, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-30 20:51:32", "test_results": {"wand_type": 12, "value1": 3.8, "value2": 3.6, "value3": 87.26, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221697, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-31 19:37:30", "test_results": {"wand_type": 16, "value1": 7.56, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023051601", "id": 10221696, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-10-31 20:42:07", "test_results": {"wand_type": 12, "value1": 3.75, "value2": 3.58, "value3": 117.58, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221695, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-01 22:40:31", "test_results": {"wand_type": 16, "value1": 11.5, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221694, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-01 23:02:33", "test_results": {"wand_type": 12, "value1": 4.97, "value2": 4.86, "value3": 153.01, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221693, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-02 19:03:36", "test_results": {"wand_type": 16, "value1": 8.23, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221692, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-02 19:25:33", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 1.32, "value3": 121.2, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221691, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-05 18:26:22", "test_results": {"wand_type": 16, "value1": 33.76, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221690, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-05 22:20:25", "test_results": {"wand_type": 12, "value1": 26.84, "value2": 4.13, "value3": 278.06, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221689, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-06 19:18:10", "test_results": {"wand_type": 16, "value1": 40.31, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221688, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-06 19:40:51", "test_results": {"wand_type": 12, "value1": 30.98, "value2": 2.42, "value3": 579.84, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221687, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-07 19:08:22", "test_results": {"wand_type": 12, "value1": 20.15, "value2": 12.56, "value3": 567.62, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221686, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-08 19:02:42", "test_results": {"wand_type": 12, "value1": 5.84, "value2": 5.75, "value3": 131.33, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221685, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-09 19:12:10", "test_results": {"wand_type": 12, "value1": 3.24, "value2": 2.6, "value3": 89.97, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221684, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-10 20:34:01", "test_results": {"wand_type": 12, "value1": 3.29, "value2": 11.5, "value3": 171.02, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221683, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-11 23:43:48", "test_results": {"wand_type": 12, "value1": 6.28, "value2": 30.01, "value3": 319.49, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221682, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-12 20:11:18", "test_results": {"wand_type": 12, "value1": 4.08, "value2": 30.01, "value3": 242.22, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221681, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-13 21:06:57", "test_results": {"wand_type": 12, "value1": 3.81, "value2": 20.59, "value3": 145.26, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221680, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-14 19:31:46", "test_results": {"wand_type": 12, "value1": 3.63, "value2": 30.01, "value3": 352.59, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221679, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-15 19:05:51", "test_results": {"wand_type": 12, "value1": 4.13, "value2": 25.43, "value3": 261.67, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221678, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-16 19:50:35", "test_results": {"wand_type": 12, "value1": 4.88, "value2": 20.02, "value3": 310.67, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221676, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-17 19:18:16", "test_results": {"wand_type": 12, "value1": 3.52, "value2": 6.46, "value3": 124.81, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221675, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-20 19:24:27", "test_results": {"wand_type": 12, "value1": 2.8, "value2": 4.47, "value3": 105.44, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221674, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-29 21:31:15", "test_results": {"wand_type": 16, "value1": 11.01, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221673, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-29 21:58:10", "test_results": {"wand_type": 12, "value1": 4, "value2": 5.81, "value3": 132.62, "value4": null, "Ecode": null}, "wandBatch3": "2023081202", "id": 10221672, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-30 21:21:28", "test_results": {"wand_type": 16, "value1": 9.08, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221671, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-11-30 23:00:27", "test_results": {"wand_type": 12, "value1": 1.74, "value2": 22.26, "value3": 300.1, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221670, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-01 23:30:21", "test_results": {"wand_type": 16, "value1": 8.63, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221669, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-01 23:58:38", "test_results": {"wand_type": 12, "value1": 1.95, "value2": 13.48, "value3": 288.36, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221668, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-03 00:11:49", "test_results": {"wand_type": 16, "value1": 11.46, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221667, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-03 00:34:30", "test_results": {"wand_type": 12, "value1": 4.02, "value2": 6, "value3": 343.59, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221666, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-03 19:58:33", "test_results": {"wand_type": 16, "value1": 12.77, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221665, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-03 21:28:17", "test_results": {"wand_type": 12, "value1": 3.42, "value2": 4.56, "value3": 359.69, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221664, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-04 21:01:40", "test_results": {"wand_type": 16, "value1": 103.6, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221663, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-04 21:25:18", "test_results": {"wand_type": 12, "value1": 67.37, "value2": 2.56, "value3": 460.22, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221662, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-05 22:41:15", "test_results": {"wand_type": 16, "value1": 18.08, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221661, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-05 23:04:18", "test_results": {"wand_type": 12, "value1": 7.41, "value2": 5.18, "value3": 446.97, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221660, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-06 23:03:25", "test_results": {"wand_type": null, "value1": null, "value2": null, "value3": null, "value4": null, "Ecode": "W01"}, "wandBatch3": "2000000000", "id": 10221659, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-06 23:19:43", "test_results": {"wand_type": 16, "value1": 1, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221658, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-06 23:41:31", "test_results": {"wand_type": 12, "value1": 2.57, "value2": 8.66, "value3": 317.02, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221657, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-08 00:11:40", "test_results": {"wand_type": 12, "value1": 1.07, "value2": 1.88, "value3": 95.69, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221656, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-09 00:13:36", "test_results": {"wand_type": 12, "value1": 1.03, "value2": 3.05, "value3": 132.21, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221655, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-10 03:45:02", "test_results": {"wand_type": 12, "value1": 1.97, "value2": 8.84, "value3": 306.48, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221654, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-11 01:17:18", "test_results": {"wand_type": 12, "value1": 1.55, "value2": 15.24, "value3": 365.6, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221653, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-11 21:39:17", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 5.69, "value3": 106.45, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221652, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-12 22:46:00", "test_results": {"wand_type": 12, "value1": 1.17, "value2": 30.01, "value3": 234.63, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221651, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-13 22:39:49", "test_results": {"wand_type": 12, "value1": 1.19, "value2": 9.77, "value3": 165.49, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221650, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-14 23:36:25", "test_results": {"wand_type": 12, "value1": 1.54, "value2": 30.01, "value3": 309.77, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221649, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-15 23:50:07", "test_results": {"wand_type": 12, "value1": 1.64, "value2": 30.01, "value3": 627.18, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221648, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-16 23:26:35", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 12.2, "value3": 258.53, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221647, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-18 22:46:56", "test_results": {"wand_type": 12, "value1": 1.02, "value2": 7.96, "value3": 187.72, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221646, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2023-12-20 02:20:57", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 2.9, "value3": 128.13, "value4": null, "Ecode": null}, "wandBatch3": "2023091201", "id": 10221645, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-20 19:58:16", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 2.75, "value3": 154.08, "value4": null, "Ecode": "B01"}, "wandBatch3": "2023091201", "id": 10221644, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-20 20:16:48", "test_results": {"wand_type": 16, "value1": 3.79, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221643, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-21 19:59:36", "test_results": {"wand_type": 12, "value1": 3.78, "value2": 1.68, "value3": 96.86, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221642, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-21 20:48:17", "test_results": {"wand_type": 16, "value1": 7.79, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221641, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-22 18:29:07", "test_results": {"wand_type": 12, "value1": 4.36, "value2": 1.43, "value3": 114.22, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221640, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-22 19:12:30", "test_results": {"wand_type": 16, "value1": 9.75, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221639, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-23 20:06:38", "test_results": {"wand_type": 12, "value1": 3.16, "value2": 1.39, "value3": 106.71, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221638, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-23 20:23:37", "test_results": {"wand_type": 16, "value1": 1, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221637, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-25 10:29:05", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 1.34, "value3": 152.91, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221636, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-25 14:36:22", "test_results": {"wand_type": 16, "value1": 6.39, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221629, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-26 19:23:26", "test_results": {"wand_type": 12, "value1": 8.47, "value2": 1.63, "value3": 224.96, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221628, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-26 21:32:14", "test_results": {"wand_type": 16, "value1": 11.95, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221627, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-27 18:05:28", "test_results": {"wand_type": 12, "value1": 22.38, "value2": 3.2, "value3": 210.15, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221626, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-27 19:38:51", "test_results": {"wand_type": 16, "value1": 14.66, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221625, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-29 00:12:24", "test_results": {"wand_type": 12, "value1": 4.63, "value2": 3.1, "value3": 202.13, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221624, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-29 00:37:11", "test_results": {"wand_type": 16, "value1": 4.2, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221623, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-29 19:17:35", "test_results": {"wand_type": 12, "value1": 3.67, "value2": 1.68, "value3": 80.06, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221622, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-02-29 21:25:08", "test_results": {"wand_type": 16, "value1": 2.29, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221621, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-01 18:07:15", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 2.22, "value3": 122.83, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221620, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-02 22:36:12", "test_results": {"wand_type": 12, "value1": 3.86, "value2": 10, "value3": 180.39, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221619, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-03 21:28:21", "test_results": {"wand_type": 12, "value1": 4.14, "value2": 23.79, "value3": 289.12, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221618, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-04 18:01:21", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 5.83, "value3": 93.05, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221617, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-05 23:33:59", "test_results": {"wand_type": 12, "value1": 3.93, "value2": 4.18, "value3": 87.51, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221616, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-06 18:07:46", "test_results": {"wand_type": 12, "value1": 1.99, "value2": 5.88, "value3": 88.69, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221615, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-07 20:46:59", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 10.1, "value3": 127.46, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221614, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-08 19:08:46", "test_results": {"wand_type": 12, "value1": 2.66, "value2": 6.7, "value3": 137.27, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221613, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-09 23:02:22", "test_results": {"wand_type": 12, "value1": 3.12, "value2": 10.97, "value3": 142.66, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221612, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-10 19:49:20", "test_results": {"wand_type": 12, "value1": 3.08, "value2": 4.8, "value3": 103.81, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221611, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-11 18:07:30", "test_results": {"wand_type": 12, "value1": 3.46, "value2": 6.12, "value3": 147.69, "value4": null, "Ecode": null}, "wandBatch3": "2023121202", "id": 10221610, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-20 18:56:30", "test_results": {"wand_type": 16, "value1": 7.76, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221609, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-20 19:29:03", "test_results": {"wand_type": 9, "value1": 0.99, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221608, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-21 18:05:28", "test_results": {"wand_type": 16, "value1": 9.22, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221607, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-21 19:55:03", "test_results": {"wand_type": 9, "value1": 3.65, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221606, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-22 20:49:59", "test_results": {"wand_type": 16, "value1": 18.58, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081601", "id": 10221605, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-22 21:22:56", "test_results": {"wand_type": 9, "value1": 1.22, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221604, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-23 22:04:45", "test_results": {"wand_type": 16, "value1": 7.51, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221603, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-23 23:11:00", "test_results": {"wand_type": 9, "value1": 0.99, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221602, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-25 22:30:12", "test_results": {"wand_type": 16, "value1": 6.41, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221601, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-25 22:51:54", "test_results": {"wand_type": 9, "value1": 2.35, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221600, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-26 20:15:03", "test_results": {"wand_type": 16, "value1": 8.1, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221599, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-26 21:48:38", "test_results": {"wand_type": 9, "value1": 1.14, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221598, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-27 17:39:46", "test_results": {"wand_type": 16, "value1": 36.73, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221597, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-27 18:44:37", "test_results": {"wand_type": 9, "value1": 13, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221596, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-28 19:52:10", "test_results": {"wand_type": 16, "value1": 14.2, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221595, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-28 21:06:03", "test_results": {"wand_type": 9, "value1": 4.74, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221594, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-29 21:44:22", "test_results": {"wand_type": 16, "value1": 5.88, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221593, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-29 22:49:11", "test_results": {"wand_type": 9, "value1": 3.31, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221592, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-31 22:30:41", "test_results": {"wand_type": 16, "value1": 2.59, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221591, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-03-31 22:47:44", "test_results": {"wand_type": 9, "value1": 3.23, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221590, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-04-03 22:10:00", "test_results": {"wand_type": 9, "value1": 6.88, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221589, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-04-03 22:28:53", "test_results": {"wand_type": 16, "value1": 3.48, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221588, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-24 19:05:56", "test_results": {"wand_type": 9, "value1": 2.05, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221587, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-24 19:22:46", "test_results": {"wand_type": 16, "value1": 8.37, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221586, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-27 22:41:33", "test_results": {"wand_type": 16, "value1": 2.77, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221585, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-27 23:16:06", "test_results": {"wand_type": 9, "value1": 0.99, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221584, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-29 22:38:53", "test_results": {"wand_type": 9, "value1": 10.2, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221583, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-29 23:31:33", "test_results": {"wand_type": 16, "value1": 8.41, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023081603", "id": 10221582, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-30 23:01:33", "test_results": {"wand_type": 9, "value1": 16.49, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221581, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-05-31 21:06:50", "test_results": {"wand_type": 9, "value1": 15.48, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221580, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-06-01 22:10:55", "test_results": {"wand_type": 9, "value1": 30.01, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221579, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-06-02 21:20:22", "test_results": {"wand_type": 9, "value1": 9.1, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221578, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-06-04 00:04:13", "test_results": {"wand_type": 9, "value1": 13.77, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221577, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2024-06-04 22:15:24", "test_results": {"wand_type": 9, "value1": 30.01, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2023090901", "id": 10221576, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-04 23:26:44", "test_results": {"wand_type": 16, "value1": 4.9, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221575, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-04 23:48:18", "test_results": {"wand_type": 12, "value1": 3.85, "value2": 3.36, "value3": 129.02, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221574, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-05 21:05:43", "test_results": {"wand_type": 12, "value1": 3.9, "value2": 2.22, "value3": 119.26, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221573, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-05 21:22:57", "test_results": {"wand_type": 16, "value1": 5.66, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221572, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-06 21:01:05", "test_results": {"wand_type": 12, "value1": 3.89, "value2": 1.68, "value3": 109.39, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221571, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-06 21:36:37", "test_results": {"wand_type": 16, "value1": 4.18, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221570, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-07 19:14:40", "test_results": {"wand_type": 12, "value1": 5.72, "value2": 2.22, "value3": 152.91, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221569, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-07 19:30:32", "test_results": {"wand_type": 16, "value1": -1, "value2": null, "value3": null, "value4": null, "Ecode": "E01"}, "wandBatch3": "2024111601", "id": 10221568, "flag": 0, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-07 20:10:10", "test_results": {"wand_type": 16, "value1": 6.98, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221565, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-08 20:53:17", "test_results": {"wand_type": 12, "value1": 37.21, "value2": 3.7, "value3": 274.3, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221564, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-08 21:09:58", "test_results": {"wand_type": 16, "value1": 49.58, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221563, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-09 19:01:00", "test_results": {"wand_type": 12, "value1": 8.16, "value2": 3.19, "value3": 118.01, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221562, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-09 19:18:03", "test_results": {"wand_type": 16, "value1": 15.22, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221561, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-10 20:00:03", "test_results": {"wand_type": 16, "value1": 6.36, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221560, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-10 20:22:11", "test_results": {"wand_type": 12, "value1": 3.84, "value2": 0.99, "value3": 31.25, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221559, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-11 19:15:46", "test_results": {"wand_type": 12, "value1": 4.36, "value2": 9.64, "value3": 97.53, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221558, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-11 19:33:41", "test_results": {"wand_type": 16, "value1": 4.83, "value2": null, "value3": null, "value4": null, "Ecode": null}, "wandBatch3": "2024111601", "id": 10221557, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-12 21:54:44", "test_results": {"wand_type": 12, "value1": 3.6, "value2": 5.52, "value3": 75.16, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221556, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-13 19:18:26", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 3.3, "value3": 64.24, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221555, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-14 21:42:18", "test_results": {"wand_type": 12, "value1": 0.99, "value2": 1.47, "value3": 39.72, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221554, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-16 00:14:29", "test_results": {"wand_type": 12, "value1": 4.19, "value2": 14.57, "value3": 105.2, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221553, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-16 23:35:36", "test_results": {"wand_type": 12, "value1": 3.89, "value2": 18.7, "value3": 102.77, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221552, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-17 19:35:26", "test_results": {"wand_type": 12, "value1": 4.76, "value2": 10.59, "value3": 69.48, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221551, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-18 20:39:16", "test_results": {"wand_type": 12, "value1": 3.34, "value2": 17.68, "value3": 102.23, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221550, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-19 21:03:16", "test_results": {"wand_type": 12, "value1": 2.39, "value2": 27.25, "value3": 157.98, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221549, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-20 19:27:16", "test_results": {"wand_type": 12, "value1": 4.93, "value2": 21.98, "value3": 90.61, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221548, "flag": 1, "extra_info": {"hcg_limitupper2": null}}, {"test_time": "2025-02-21 21:56:19", "test_results": {"wand_type": 12, "value1": 3.51, "value2": 3.91, "value3": 74.52, "value4": null, "Ecode": null}, "wandBatch3": "2024101208", "id": 10221547, "flag": 1, "extra_info": {"hcg_limitupper2": null}}], "cycle_data": [{"cycle_index": 0, "len_cycle": 32, "date_period_start": "2023-05-23", "date_period_end": "2023-05-26", "date_FW_start": "2023-06-06", "date_FW_end": "2023-06-13", "date_ovulation": "2023-06-10", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 16, "threshold_E3G": 100, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30", "CD31", "CD32"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-05-27", "2023-05-29", "2023-05-31", "2023-06-02", "2023-06-04", "2023-06-06", "2023-06-07", "2023-06-08", "2023-06-09", "2023-06-10", "2023-06-11", "2023-06-12", "2023-06-13", "2023-06-14", "2023-06-15", "2023-06-16", "2023-06-17", "2023-06-18", "2023-06-20", "2023-06-21", "2023-06-23"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-05-27", "2023-05-29", "2023-05-31", "2023-06-02", "2023-06-04", "2023-06-06", "2023-06-07", "2023-06-08", "2023-06-09", "2023-06-10", "2023-06-11", "2023-06-12", "2023-06-13", "2023-06-14", "2023-06-15", "2023-06-16", "2023-06-17", "2023-06-18", "2023-06-20", "2023-06-21", "2023-06-23"]}}, {"cycle_index": 1, "len_cycle": 29, "date_period_start": "2023-06-24", "date_period_end": "2023-06-27", "date_FW_start": "2023-07-06", "date_FW_end": "2023-07-13", "date_ovulation": "2023-07-10", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": ["2023-07-11"], "threshold_LH": 15, "threshold_E3G": 85, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-06-28", "2023-06-30", "2023-07-02", "2023-07-04", "2023-07-06", "2023-07-07", "2023-07-08", "2023-07-09", "2023-07-10", "2023-07-11", "2023-07-12", "2023-07-13", "2023-07-14", "2023-07-15", "2023-07-17", "2023-07-21"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-06-28", "2023-06-30", "2023-07-02", "2023-07-04", "2023-07-06", "2023-07-07", "2023-07-08", "2023-07-09", "2023-07-10", "2023-07-11", "2023-07-12", "2023-07-13", "2023-07-14", "2023-07-15", "2023-07-17", "2023-07-21"]}}, {"cycle_index": 2, "len_cycle": 29, "date_period_start": "2023-07-23", "date_period_end": "2023-07-27", "date_FW_start": "2023-08-04", "date_FW_end": "2023-08-11", "date_ovulation": "2023-08-08", "date_ovulation_multi": null, "date_LH_surge": "2023-08-08 11:40:15", "value_LH_surge": 16.02, "ovulation_type": 23, "date_PDG_rise": ["2023-08-11", "2023-08-12", "2023-08-13", "2023-08-17", "2023-08-18", "2023-08-19"], "threshold_LH": 14, "threshold_E3G": 139, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 10, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-07-28", "2023-07-30", "2023-08-01", "2023-08-03", "2023-08-04", "2023-08-05", "2023-08-06", "2023-08-07", "2023-08-08", "2023-08-09", "2023-08-11", "2023-08-13", "2023-08-15", "2023-08-19"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-07-28", "2023-07-30", "2023-08-01", "2023-08-03", "2023-08-04", "2023-08-05", "2023-08-06", "2023-08-07", "2023-08-08", "2023-08-09", "2023-08-11", "2023-08-13", "2023-08-15", "2023-08-19"]}}, {"cycle_index": 3, "len_cycle": 28, "date_period_start": "2023-08-21", "date_period_end": "2023-08-24", "date_FW_start": "2023-08-31", "date_FW_end": "2023-09-07", "date_ovulation": "2023-09-04", "date_ovulation_multi": null, "date_LH_surge": "2023-09-03 22:02:06", "value_LH_surge": 49.09, "ovulation_type": 23, "date_PDG_rise": ["2023-09-06", "2023-09-07", "2023-09-14", "2023-09-16"], "threshold_LH": 14, "threshold_E3G": 200, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 10, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-08-25", "2023-08-27", "2023-08-29", "2023-08-30", "2023-08-31", "2023-09-01", "2023-09-02", "2023-09-03", "2023-09-04", "2023-09-05", "2023-09-06", "2023-09-07", "2023-09-08", "2023-09-09", "2023-09-10", "2023-09-14", "2023-09-16"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-08-25", "2023-08-27", "2023-08-29", "2023-08-30", "2023-08-31", "2023-09-01", "2023-09-02", "2023-09-03", "2023-09-04", "2023-09-05", "2023-09-06", "2023-09-07", "2023-09-08", "2023-09-09", "2023-09-10", "2023-09-14", "2023-09-16"]}}, {"cycle_index": 4, "len_cycle": 32, "date_period_start": "2023-09-18", "date_period_end": "2023-09-22", "date_FW_start": "2023-10-04", "date_FW_end": "2023-10-11", "date_ovulation": "2023-10-08", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 87, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30", "CD31", "CD32"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-09-23", "2023-09-25", "2023-09-27", "2023-09-29", "2023-10-01", "2023-10-03", "2023-10-04", "2023-10-05", "2023-10-06", "2023-10-07", "2023-10-08", "2023-10-09", "2023-10-10", "2023-10-11", "2023-10-12", "2023-10-13", "2023-10-14", "2023-10-15", "2023-10-16", "2023-10-18", "2023-10-19"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-09-23", "2023-09-25", "2023-09-27", "2023-09-29", "2023-10-01", "2023-10-03", "2023-10-04", "2023-10-05", "2023-10-06", "2023-10-07", "2023-10-08", "2023-10-09", "2023-10-10", "2023-10-11", "2023-10-12", "2023-10-13", "2023-10-14", "2023-10-15", "2023-10-16", "2023-10-18", "2023-10-19"]}}, {"cycle_index": 5, "len_cycle": 32, "date_period_start": "2023-10-20", "date_period_end": "2023-10-24", "date_FW_start": "2023-11-03", "date_FW_end": "2023-11-10", "date_ovulation": "2023-11-07", "date_ovulation_multi": null, "date_LH_surge": "2023-11-06 19:40:51", "value_LH_surge": 30.98, "ovulation_type": 23, "date_PDG_rise": ["2023-11-07", "2023-11-11", "2023-11-12", "2023-11-13", "2023-11-14"], "threshold_LH": 14, "threshold_E3G": 137, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30", "CD31", "CD32"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 10, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-10-25", "2023-10-27", "2023-10-29", "2023-10-31", "2023-11-02", "2023-11-03", "2023-11-04", "2023-11-05", "2023-11-06", "2023-11-07", "2023-11-09", "2023-11-11", "2023-11-13", "2023-11-17", "2023-11-19"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-10-25", "2023-10-27", "2023-10-29", "2023-10-31", "2023-11-02", "2023-11-03", "2023-11-04", "2023-11-05", "2023-11-06", "2023-11-07", "2023-11-09", "2023-11-11", "2023-11-13", "2023-11-17", "2023-11-19"]}}, {"cycle_index": 6, "len_cycle": 29, "date_period_start": "2023-11-21", "date_period_end": "2023-11-26", "date_FW_start": "2023-12-01", "date_FW_end": "2023-12-08", "date_ovulation": "2023-12-05", "date_ovulation_multi": null, "date_LH_surge": "2023-12-04 21:25:18", "value_LH_surge": 67.37, "ovulation_type": 23, "date_PDG_rise": ["2023-12-11", "2023-12-12", "2023-12-14", "2023-12-15"], "threshold_LH": 14, "threshold_E3G": 200, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 5, 7, 8, 10, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-11-27", "2023-11-29", "2023-11-30", "2023-12-01", "2023-12-02", "2023-12-03", "2023-12-04", "2023-12-05", "2023-12-06", "2023-12-07", "2023-12-08", "2023-12-09", "2023-12-10", "2023-12-11", "2023-12-12", "2023-12-15", "2023-12-17", "2023-12-19"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-11-27", "2023-11-29", "2023-11-30", "2023-12-01", "2023-12-02", "2023-12-03", "2023-12-04", "2023-12-05", "2023-12-06", "2023-12-07", "2023-12-08", "2023-12-09", "2023-12-10", "2023-12-11", "2023-12-12", "2023-12-15", "2023-12-17", "2023-12-19"]}}, {"cycle_index": 7, "len_cycle": 26, "date_period_start": "2023-12-20", "date_period_end": "2023-12-25", "date_FW_start": "2023-12-27", "date_FW_end": "2024-01-02", "date_ovulation": "2023-12-30", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 200, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2023-12-26", "2023-12-27", "2023-12-28", "2023-12-29", "2023-12-30", "2023-12-31", "2024-01-01", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-06", "2024-01-07", "2024-01-09", "2024-01-10", "2024-01-12", "2024-01-13"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2023-12-26", "2023-12-27", "2023-12-28", "2023-12-29", "2023-12-30", "2023-12-31", "2024-01-01", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-06", "2024-01-07", "2024-01-09", "2024-01-10", "2024-01-12", "2024-01-13"]}}, {"cycle_index": 8, "len_cycle": 28, "date_period_start": "2024-01-15", "date_period_end": "2024-01-21", "date_FW_start": "2024-01-23", "date_FW_end": "2024-01-30", "date_ovulation": "2024-01-27", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 200, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 1, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-27", "2024-01-28", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-03", "2024-02-04", "2024-02-06", "2024-02-07", "2024-02-09", "2024-02-10", "2024-02-11"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-27", "2024-01-28", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-03", "2024-02-04", "2024-02-06", "2024-02-07", "2024-02-09", "2024-02-10", "2024-02-11"]}}, {"cycle_index": 9, "len_cycle": 29, "date_period_start": "2024-02-12", "date_period_end": "2024-02-17", "date_FW_start": "2024-02-24", "date_FW_end": "2024-03-02", "date_ovulation": "2024-02-28", "date_ovulation_multi": null, "date_LH_surge": "2024-02-27 18:05:28", "value_LH_surge": 22.38, "ovulation_type": 23, "date_PDG_rise": ["2024-03-03"], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 10, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-02-18", "2024-02-20", "2024-02-22", "2024-02-23", "2024-02-24", "2024-02-25", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-02", "2024-03-03", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-09", "2024-03-11"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-02-18", "2024-02-20", "2024-02-22", "2024-02-23", "2024-02-24", "2024-02-25", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-02", "2024-03-03", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-09", "2024-03-11"]}}, {"cycle_index": 10, "len_cycle": 29, "date_period_start": "2024-03-12", "date_period_end": "2024-03-17", "date_FW_start": "2024-03-22", "date_FW_end": "2024-03-29", "date_ovulation": "2024-03-26", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": ["2024-03-27"], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-03-18", "2024-03-20", "2024-03-22", "2024-03-23", "2024-03-24", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-03-29", "2024-03-30", "2024-03-31", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-05", "2024-04-06", "2024-04-08", "2024-04-09"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-03-18", "2024-03-20", "2024-03-22", "2024-03-23", "2024-03-24", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-03-29", "2024-03-30", "2024-03-31", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-05", "2024-04-06", "2024-04-08", "2024-04-09"]}}, {"cycle_index": 11, "len_cycle": 31, "date_period_start": "2024-04-10", "date_period_end": "2024-04-15", "date_FW_start": "2024-04-22", "date_FW_end": "2024-04-29", "date_ovulation": "2024-04-26", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30", "CD31"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-04-16", "2024-04-18", "2024-04-20", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-27", "2024-04-28", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-04", "2024-05-06", "2024-05-07", "2024-05-09", "2024-05-10"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-04-16", "2024-04-18", "2024-04-20", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-27", "2024-04-28", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-04", "2024-05-06", "2024-05-07", "2024-05-09", "2024-05-10"]}}, {"cycle_index": 12, "len_cycle": 31, "date_period_start": "2024-05-11", "date_period_end": "2024-05-16", "date_FW_start": "2024-05-23", "date_FW_end": "2024-05-30", "date_ovulation": "2024-05-27", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": ["2024-05-30", "2024-05-31", "2024-06-01", "2024-06-04"], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30", "CD31"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-05-17", "2024-05-19", "2024-05-21", "2024-05-23", "2024-05-24", "2024-05-25", "2024-05-26", "2024-05-27", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-01", "2024-06-02", "2024-06-03", "2024-06-07", "2024-06-09"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-05-17", "2024-05-19", "2024-05-21", "2024-05-23", "2024-05-24", "2024-05-25", "2024-05-26", "2024-05-27", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-01", "2024-06-02", "2024-06-03", "2024-06-07", "2024-06-09"]}}, {"cycle_index": 13, "len_cycle": 28, "date_period_start": "2024-06-11", "date_period_end": "2024-06-16", "date_FW_start": "2024-06-20", "date_FW_end": "2024-06-27", "date_ovulation": "2024-06-24", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-06-17", "2024-06-19", "2024-06-20", "2024-06-21", "2024-06-22", "2024-06-23", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-06-29", "2024-06-30", "2024-07-01", "2024-07-02", "2024-07-04", "2024-07-05", "2024-07-07", "2024-07-08"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-06-17", "2024-06-19", "2024-06-20", "2024-06-21", "2024-06-22", "2024-06-23", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-06-29", "2024-06-30", "2024-07-01", "2024-07-02", "2024-07-04", "2024-07-05", "2024-07-07", "2024-07-08"]}}, {"cycle_index": 14, "len_cycle": 28, "date_period_start": "2024-07-09", "date_period_end": "2024-07-14", "date_FW_start": "2024-07-18", "date_FW_end": "2024-07-25", "date_ovulation": "2024-07-22", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-07-15", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-20", "2024-07-21", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-27", "2024-07-28", "2024-07-29", "2024-07-30", "2024-08-01", "2024-08-02", "2024-08-04", "2024-08-05"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-07-15", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-20", "2024-07-21", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-27", "2024-07-28", "2024-07-29", "2024-07-30", "2024-08-01", "2024-08-02", "2024-08-04", "2024-08-05"]}}, {"cycle_index": 15, "len_cycle": 31, "date_period_start": "2024-08-06", "date_period_end": "2024-08-11", "date_FW_start": "2024-08-18", "date_FW_end": "2024-08-25", "date_ovulation": "2024-08-22", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30", "CD31"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-08-12", "2024-08-14", "2024-08-16", "2024-08-18", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-24", "2024-08-25", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-01", "2024-09-02", "2024-09-04", "2024-09-05"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-08-12", "2024-08-14", "2024-08-16", "2024-08-18", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-24", "2024-08-25", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-01", "2024-09-02", "2024-09-04", "2024-09-05"]}}, {"cycle_index": 16, "len_cycle": 30, "date_period_start": "2024-09-06", "date_period_end": "2024-09-11", "date_FW_start": "2024-09-17", "date_FW_end": "2024-09-24", "date_ovulation": "2024-09-21", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-09-12", "2024-09-14", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-21", "2024-09-22", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-09-28", "2024-09-29", "2024-10-01", "2024-10-02", "2024-10-04", "2024-10-05"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-09-12", "2024-09-14", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-21", "2024-09-22", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-09-28", "2024-09-29", "2024-10-01", "2024-10-02", "2024-10-04", "2024-10-05"]}}, {"cycle_index": 17, "len_cycle": 30, "date_period_start": "2024-10-06", "date_period_end": "2024-10-11", "date_FW_start": "2024-10-17", "date_FW_end": "2024-10-24", "date_ovulation": "2024-10-21", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-10-12", "2024-10-14", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-19", "2024-10-20", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-26", "2024-10-27", "2024-10-28", "2024-10-29", "2024-10-31", "2024-11-01", "2024-11-03", "2024-11-04"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-10-12", "2024-10-14", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-19", "2024-10-20", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-26", "2024-10-27", "2024-10-28", "2024-10-29", "2024-10-31", "2024-11-01", "2024-11-03", "2024-11-04"]}}, {"cycle_index": 18, "len_cycle": 29, "date_period_start": "2024-11-05", "date_period_end": "2024-11-10", "date_FW_start": "2024-11-15", "date_FW_end": "2024-11-22", "date_ovulation": "2024-11-19", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-11-11", "2024-11-13", "2024-11-15", "2024-11-16", "2024-11-17", "2024-11-18", "2024-11-19", "2024-11-20", "2024-11-21", "2024-11-22", "2024-11-23", "2024-11-24", "2024-11-25", "2024-11-26", "2024-11-27", "2024-11-29", "2024-11-30", "2024-12-02", "2024-12-03"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-11-11", "2024-11-13", "2024-11-15", "2024-11-16", "2024-11-17", "2024-11-18", "2024-11-19", "2024-11-20", "2024-11-21", "2024-11-22", "2024-11-23", "2024-11-24", "2024-11-25", "2024-11-26", "2024-11-27", "2024-11-29", "2024-11-30", "2024-12-02", "2024-12-03"]}}, {"cycle_index": 19, "len_cycle": 25, "date_period_start": "2024-12-04", "date_period_end": "2024-12-09", "date_FW_start": "2024-12-11", "date_FW_end": "2024-12-17", "date_ovulation": "2024-12-14", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2024-12-10", "2024-12-11", "2024-12-12", "2024-12-13", "2024-12-14", "2024-12-15", "2024-12-16", "2024-12-17", "2024-12-18", "2024-12-19", "2024-12-20", "2024-12-21", "2024-12-22", "2024-12-24", "2024-12-25", "2024-12-27"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2024-12-10", "2024-12-11", "2024-12-12", "2024-12-13", "2024-12-14", "2024-12-15", "2024-12-16", "2024-12-17", "2024-12-18", "2024-12-19", "2024-12-20", "2024-12-21", "2024-12-22", "2024-12-24", "2024-12-25", "2024-12-27"]}}, {"cycle_index": 20, "len_cycle": 30, "date_period_start": "2024-12-29", "date_period_end": "2025-01-03", "date_FW_start": "2025-01-09", "date_FW_end": "2025-01-16", "date_ovulation": "2025-01-13", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 129, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29", "CD30"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2025-01-04", "2025-01-06", "2025-01-08", "2025-01-09", "2025-01-10", "2025-01-11", "2025-01-12", "2025-01-13", "2025-01-14", "2025-01-15", "2025-01-16", "2025-01-17", "2025-01-18", "2025-01-19", "2025-01-20", "2025-01-21", "2025-01-23", "2025-01-24", "2025-01-26", "2025-01-27"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2025-01-04", "2025-01-06", "2025-01-08", "2025-01-09", "2025-01-10", "2025-01-11", "2025-01-12", "2025-01-13", "2025-01-14", "2025-01-15", "2025-01-16", "2025-01-17", "2025-01-18", "2025-01-19", "2025-01-20", "2025-01-21", "2025-01-23", "2025-01-24", "2025-01-26", "2025-01-27"]}}, {"cycle_index": 21, "len_cycle": 26, "date_period_start": "2025-01-28", "date_period_end": "2025-02-02", "date_FW_start": "2025-02-05", "date_FW_end": "2025-02-12", "date_ovulation": "2025-02-09", "date_ovulation_multi": null, "date_LH_surge": "2025-02-08 20:53:17", "value_LH_surge": 37.21, "ovulation_type": 23, "date_PDG_rise": ["2025-02-16", "2025-02-18", "2025-02-19", "2025-02-20"], "threshold_LH": 14, "threshold_E3G": 114, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 3, 4, 5, 7, 8, 10, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2025-02-03", "2025-02-04", "2025-02-05", "2025-02-06", "2025-02-07", "2025-02-08", "2025-02-09", "2025-02-10", "2025-02-11", "2025-02-12", "2025-02-13", "2025-02-14", "2025-02-15", "2025-02-16", "2025-02-18", "2025-02-22"], "product14": [], "product16": [], "product18": [], "algorithm12": ["2025-02-03", "2025-02-04", "2025-02-05", "2025-02-06", "2025-02-07", "2025-02-08", "2025-02-09", "2025-02-10", "2025-02-11", "2025-02-12", "2025-02-13", "2025-02-14", "2025-02-15", "2025-02-16", "2025-02-18", "2025-02-22"]}}, {"cycle_index": 22, "len_cycle": 68, "date_period_start": "2025-02-23", "date_period_end": "2025-02-28", "date_FW_start": null, "date_FW_end": null, "date_ovulation": null, "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": null, "date_PDG_rise": [], "threshold_LH": null, "threshold_E3G": null, "cycle_status": 9, "len_phase": [68], "cycle_cd_index": ["Day 1", "Day 2", "Day 3", "Day 4", "Day 5", "Day 6", "Day 7", "Wk 1", null, null, null, null, null, null, "Wk 2", null, null, null, null, null, null, "Wk 3", null, null, null, null, null, null, "Wk 4", null, null, null, null, null, null, "Wk 5", null, null, null, null, null, null, "Wk 6", null, null, null, null, null, null, "Wk 7", null, null, null, null, null, null, "Wk 8", null, null, null, null, null, null, "Wk 9", null, null, null, null], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": [], "product14": [], "product16": [], "product18": [], "algorithm12": []}}, {"cycle_index": 23, "len_cycle": 39, "date_period_start": "2025-05-02", "date_period_end": null, "date_FW_start": null, "date_FW_end": null, "date_ovulation": null, "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": null, "date_PDG_rise": [], "threshold_LH": null, "threshold_E3G": null, "cycle_status": 6, "len_phase": null, "cycle_cd_index": [], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": [], "product14": [], "product16": [], "product18": [], "algorithm12": ["2025-05-02", "2025-05-03", "2025-05-04", "2025-05-05", "2025-05-06", "2025-05-07", "2025-05-08", "2025-05-09", "2025-05-10", "2025-05-11", "2025-05-12", "2025-05-13", "2025-05-14", "2025-05-15", "2025-05-16", "2025-05-17", "2025-05-18", "2025-05-19", "2025-05-20", "2025-05-21", "2025-05-22", "2025-05-23", "2025-05-24", "2025-05-25", "2025-05-26", "2025-05-27", "2025-05-28", "2025-05-29", "2025-05-30", "2025-05-31", "2025-06-01", "2025-06-02", "2025-06-03", "2025-06-04", "2025-06-05", "2025-06-06", "2025-06-07", "2025-06-08", "2025-06-09"]}}, {"cycle_index": 24, "len_cycle": 29, "date_period_start": "2025-06-10", "date_period_end": "2025-06-15", "date_FW_start": "2025-06-20", "date_FW_end": "2025-06-27", "date_ovulation": "2025-06-24", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 114, "cycle_status": 1, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28", "CD29"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2025-06-16", "2025-06-18", "2025-06-20", "2025-06-21", "2025-06-22", "2025-06-23", "2025-06-24", "2025-06-25", "2025-06-26", "2025-06-27", "2025-06-28", "2025-06-29", "2025-06-30", "2025-07-01", "2025-07-02", "2025-07-04", "2025-07-05", "2025-07-07", "2025-07-08"], "product14": [], "product16": ["2025-06-16", "2025-06-18", "2025-06-20", "2025-06-21", "2025-06-22", "2025-06-23", "2025-06-24", "2025-06-25", "2025-06-26"], "product18": [], "algorithm12": ["2025-06-16", "2025-06-18", "2025-06-20", "2025-06-21", "2025-06-22", "2025-06-23", "2025-06-24", "2025-06-25", "2025-06-26", "2025-06-27", "2025-06-28", "2025-06-29", "2025-06-30", "2025-07-01", "2025-07-02", "2025-07-04", "2025-07-05", "2025-07-07", "2025-07-08"]}}, {"cycle_index": 25, "len_cycle": 28, "date_period_start": "2025-07-09", "date_period_end": "2025-07-13", "date_FW_start": "2025-07-18", "date_FW_end": "2025-07-24", "date_ovulation": "2025-07-22", "date_ovulation_multi": null, "date_LH_surge": null, "value_LH_surge": null, "ovulation_type": 1, "date_PDG_rise": [], "threshold_LH": 14, "threshold_E3G": 148, "cycle_status": 2, "len_phase": null, "cycle_cd_index": ["CD1", "CD2", "CD3", "CD4", "CD5", "CD6", "CD7", "CD8", "CD9", "CD10", "CD11", "CD12", "CD13", "CD14", "CD15", "CD16", "CD17", "CD18", "CD19", "CD20", "CD21", "CD22", "CD23", "CD24", "CD25", "CD26", "CD27", "CD28"], "pregnant_risk": {"high_risks": [], "medium_risks": [], "low_risks": []}, "fertility_score_list": [1, 1, 1, 1, 2, 2, 2, 2, 3, 4, 5, 7, 8, 9, 6, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "testing_day_list": {"product03": [], "product02": [], "product09": [], "product12": ["2025-07-14", "2025-07-16", "2025-07-18", "2025-07-19", "2025-07-20", "2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-26", "2025-07-27", "2025-07-28", "2025-07-29", "2025-07-30", "2025-08-01", "2025-08-02", "2025-08-04", "2025-08-05"], "product14": [], "product16": ["2025-07-14", "2025-07-16", "2025-07-18", "2025-07-19", "2025-07-20", "2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24"], "product18": [], "algorithm12": ["2025-07-14", "2025-07-16", "2025-07-18", "2025-07-19", "2025-07-20", "2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-26", "2025-07-27", "2025-07-28", "2025-07-29", "2025-07-30", "2025-08-01", "2025-08-02", "2025-08-04", "2025-08-05"]}}]}}}