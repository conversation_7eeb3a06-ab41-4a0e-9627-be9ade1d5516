package com.mira.user.controller.vo.calendar;

import com.mira.api.user.enums.UserGoalEnum;
import com.mira.user.controller.vo.calendar.sub.CycleDateRange;
import com.mira.user.controller.vo.calendar.sub.OvuData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel("周期详情")
public class CycleInfoVO {
    @ApiModelProperty("用户输入的经期的所有日期 json 数据")
    private List<String> periods = new ArrayList<>();

    @ApiModelProperty("预测经期")
    private List<String> predictedPeriods;

    @ApiModelProperty("平均经期长度")
    private Integer avgLenPeriod;

    /**
     * @see UserGoalEnum
     */
    @ApiModelProperty("用户的Mode")
    private Integer userMode;

    @ApiModelProperty("当前周期index")
    private Integer currentCycleIndex;

    @ApiModelProperty("周期日期范围")
    private List<CycleDateRange> cycleRange = new ArrayList<>();

    @ApiModelProperty("易孕期")
    private List<String> fw = new ArrayList<>();

    @ApiModelProperty("预测易孕期")
    private List<String> pFw = new ArrayList<>();

    @ApiModelProperty("排卵日")
    private List<OvuData> ovu = new ArrayList<>();

    @ApiModelProperty("用户自定义的排卵日")
    private List<OvuData> ovuCustom = new ArrayList<>();

    @ApiModelProperty("无经期模式，0-否，1-是")
    private Integer noPeriod = 0;
}
