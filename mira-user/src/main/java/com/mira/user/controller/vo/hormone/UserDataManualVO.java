package com.mira.user.controller.vo.hormone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-03-21
 **/
@Data
public class UserDataManualVO {
    private Long id;

    /**
     * 试剂完成时间
     */
    private String completeTime;


    /**
     * 试剂类型
     */
    private String testWandType;
    /**
     * t1浓度值
     */
    private String t1ConValue;
    /**
     * t2浓度值
     */
    private String t2ConValue;
    /**
     * t3浓度值
     */
    private String t3ConValue;
    /**
     * t4浓度值
     */
    private String t4ConValue;

    private String photoUrl1;
    private String photoUrl2;
    private String photoUrl3;
    private String photoUrl4;
    /**
     * 状态: 0:未处理;1:正在处理;2:添加成功;3:已同步;4:添加失败(默认0);5:忽略请求
     */
    @ApiModelProperty(value = "状态: 0:未处理;1:正在处理;2:Your data was added;" +
            "3:Your data was synchronized;" +
            "4:Your data was not added to your profile" +
            "5:Your data was ignored")
    private Integer status;

    /**
     * 0:等于数值；1:大于数值；2:小于数值(默认0)
     */
    private Integer data1Compare;
    private Integer data2Compare;
    private Integer data3Compare;
    private Integer data4Compare;

    private String createTimeStr;

    private String modifyTimeStr;

}
