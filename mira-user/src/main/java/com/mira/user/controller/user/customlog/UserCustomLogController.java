package com.mira.user.controller.user.customlog;

import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.user.controller.vo.diary.*;
import com.mira.user.dto.diary.*;
import com.mira.user.service.user.IUserCustomLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 用户日志控制器
 *
 * <AUTHOR>
 */
@Api(tags = "06.用户日志")
@RestController
@RequestMapping("/app/v4/custom-log")
public class UserCustomLogController {
    @Resource(name = "userCustomLogService")
    private IUserCustomLogService userCustomLogService;

    @ApiOperation("获取用户私有的Diary配置")
    @GetMapping("/config/info")
    public UserCustomLogConfigVO configInfo() {
        return userCustomLogService.configInfo();
    }

    @ApiOperation("编辑用户私有的Diary配置")
    @PostMapping("/config/update")
    public void configUpdate(@RequestBody UserCustomLogConfigDTO userCustomLogConfigDTO) {
        userCustomLogService.configUpdate(userCustomLogConfigDTO);
    }

    @ApiOperation("获取用户Diary的note记录")
    @GetMapping("/note/info")
    public String diaryNote(@RequestParam String dateStr) {
        return userCustomLogService.diaryNote(dateStr);
    }

    @ApiOperation("编辑用户Diary的note记录")
    @PostMapping("/note/update")
    public void diaryNoteUpdate(@Valid @RequestBody UserDiaryNoteDTO userDiaryNoteDTO) {
        userCustomLogService.diaryNoteUpdate(userDiaryNoteDTO);
    }

    @ApiOperation("获取用户Diary的symptom记录")
    @GetMapping("/symptom/info")
    public List<UserSymptomVO> diarySymptom(@RequestParam String dateStr) {
        return userCustomLogService.diarySymptom(dateStr);
    }

    @ApiOperation("编辑用户Diary的symptom记录")
    @PostMapping("/symptom/update")
    public void diarySymptomUpdate(@Valid @RequestBody UserDiarySymptomsDTO userDiarySymptomsDTO) {
        userCustomLogService.diarySymptomUpdate(userDiarySymptomsDTO);
    }

    @ApiOperation("获取用户Diary记录")
    @GetMapping("/info")
    public UserCustomLogVO diaryInfo(@RequestParam String dateStr) {
        return userCustomLogService.diaryInfo(dateStr);
    }

    @ApiOperation("编辑用户Diary记录")
    @PostMapping("/update")
    public UserCustomLogUpdateVO diaryUpdate(@Valid @RequestBody UserCustomLogDTO userCustomLogDTO) {
        return userCustomLogService.diaryUpdate(userCustomLogDTO);
    }

    /**
     * 调用此接口的时间是用户的今天
     */
    @ApiOperation("编辑用户Diary的pregnant记录")
    @PostMapping("/pregnant/update")
    public UserCustomLogUpdateVO pregnantUpdate(@ApiParam(name = "pregnant",
            value = "true:Positive;false:Negative") @RequestParam Boolean pregnant) {
        return userCustomLogService.pregnantUpdate(pregnant);
    }

    @ApiOperation("单位转换计算")
    @PostMapping("/unit")
    public Map<String, BigDecimal> convertUnit(@Valid @RequestBody UnitCalculationDTO unitCalculationDTO) {
        return userCustomLogService.convertUnit(unitCalculationDTO);
    }

    @ApiOperation("获取单条用户Diary温度")
    @GetMapping("/temperature")
    public UserTemperatureVO temperature(@RequestParam Long id) {
        return userCustomLogService.temperature(id);
    }

    @ApiOperation("编辑单条用户Diary温度")
    @PostMapping("/temperature/update")
    public void temperatureUpdate(@Valid @RequestBody UpdateTemperatureDTO updateTemperatureDTO) {
        userCustomLogService.temperatureUpdate(updateTemperatureDTO);
    }

    @ApiOperation("获取用户Diary的moods记录")
    @GetMapping("/moods/info")
    public UserDiaryMoodsVO moodInfo(@RequestParam String dateStr) {
        return userCustomLogService.moodInfo(dateStr);
    }

    @ApiOperation("编辑用户Diary的moods记录")
    @PostMapping("/moods/update")
    public void moodUpdate(@Valid @RequestBody UserDiaryMoodsDTO userDiaryMoodsDTO) {
        userCustomLogService.moodUpdate(userDiaryMoodsDTO);
    }

    @ApiOperation("删除单条用户的蓝牙温度")
    @PostMapping("/temperature/delete")
    public void temperatureDelete(@RequestBody DeleteTemperatureDTO deleteTemperatureDTO) {
        userCustomLogService.temperatureDelete(deleteTemperatureDTO);
    }

    @ApiOperation(value = "获取当日存在的customlog情况")
    @GetMapping("/exist")
    @Deprecated
    public CustomlogExistVO exist() {
        return null;
    }
}
