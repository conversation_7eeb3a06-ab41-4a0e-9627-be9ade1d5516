package com.mira.user.service.hormone;

import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.enums.RemindStatusConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.dal.dao.AppUserTestingScheduleDAO;
import com.mira.user.dal.dao.AppUserWandCountDAO;
import com.mira.user.dal.dao.UserReminderComplaintDAO;
import com.mira.user.dal.entity.AppUserTestingScheduleEntity;
import com.mira.user.dal.entity.AppUserWandCountEntity;
import com.mira.user.dto.wand.WandCountDTO;
import com.mira.user.service.manager.AlgorithmCallManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-12-12
 **/
@Slf4j
@Service
public class AppUserWandCountImpl implements IAppUserWandCountService {
    @Resource
    private AppUserWandCountDAO appUserWandCountDAO;
    @Resource
    private UserReminderComplaintDAO userReminderComplaintDAO;
    @Resource
    private AppUserTestingScheduleDAO appUserTestingScheduleDAO;
    @Resource
    private AlgorithmCallManager algorithmCallManager;
    @Resource
    private ISsoProvider ssoProvider;

    @Override
    public WandCountDTO wandCount() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        AppUserWandCountEntity appUserWandCount = appUserWandCountDAO.getByUserId(userId);
        WandCountDTO wandCountDTO = new WandCountDTO();
        if (appUserWandCount != null) {
            BeanUtils.copyProperties(appUserWandCount, wandCountDTO);
        }
        return wandCountDTO;
    }

    @Override
    public void setWandCount(WandCountDTO wandCountDTO) {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String email = loginInfo.getUsername();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();
        AppUserWandCountEntity appUserWandCount = appUserWandCountDAO.getByUserId(userId);
        if (appUserWandCount == null) {
            appUserWandCount = new AppUserWandCountEntity();
            appUserWandCount.setUserId(userId);
            BeanUtils.copyProperties(wandCountDTO, appUserWandCount);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, appUserWandCount);
            appUserWandCountDAO.save(appUserWandCount);
        } else {
            BeanUtils.copyProperties(wandCountDTO, appUserWandCount);
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserWandCount);
            appUserWandCountDAO.updateById(appUserWandCount);
        }

        Integer plus = wandCountDTO.getPlus();
        Integer confirm = wandCountDTO.getConfirm();
        Integer max = wandCountDTO.getMax();
        Integer ovum = wandCountDTO.getOvum();
        Integer max2 = wandCountDTO.getMax2();

        // 需要修改 testing schedule 并调用编辑经期
        AppUserTestingScheduleEntity userTestingScheduleEntity = appUserTestingScheduleDAO.getByUserId(userId);
        //userTestingScheduleEntity一定存在
        //基于用户选择的试剂，对TestingSchedule进行一次修正

        //Plus + Confirm
        //menopause 模式下 ovum的设置在bluetooth模块中，一定测试MAX会被这里修改成关闭
        if ((plus != null && plus != 0) || (confirm != null && confirm != 0)) {
            userTestingScheduleEntity.setPlus(RemindStatusConst.SCHEDULE_ON);
            userTestingScheduleEntity.setConfirm(RemindStatusConst.SCHEDULE_ON);
            userTestingScheduleEntity.setMax(RemindStatusConst.SCHEDULE_OFF);
            //如果有选择plus or confirm， 需要把testing schedule 的 max 与 plus+confirm 切换开关打开
            userReminderComplaintDAO.addTestingScheduleExchange(userId);
        }
        // Ovum
        if (ovum != null && ovum != 0) {
            userTestingScheduleEntity.setOvum(RemindStatusConst.SCHEDULE_ON);
            userTestingScheduleEntity.setMax(RemindStatusConst.SCHEDULE_ON);
        } else {
            userTestingScheduleEntity.setOvum(RemindStatusConst.SCHEDULE_OFF);
        }

        // Max
        // 只要选了 MAX，就应该把Plus + Confirm关闭
        if (max != null && max != 0) {
            if (0 == userTestingScheduleEntity.getMax()) {
                userTestingScheduleEntity.setMax(RemindStatusConst.SCHEDULE_ON);
                userTestingScheduleEntity.setPlus(RemindStatusConst.SCHEDULE_OFF);
                userTestingScheduleEntity.setConfirm(RemindStatusConst.SCHEDULE_OFF);
            }
        }

        if (Integer.valueOf(1).equals(trackingMenopause)) {
            userTestingScheduleEntity.setOvum(RemindStatusConst.SCHEDULE_ALWAYS_ON);
        }

        // 只要选了 MAX2，就应该把其他关闭
        if (max2 != null && max2 != 0) {
            userTestingScheduleEntity.setMax2(RemindStatusConst.SCHEDULE_ON);
            userTestingScheduleEntity.setOvum(RemindStatusConst.SCHEDULE_OFF);
            userTestingScheduleEntity.setMax(RemindStatusConst.SCHEDULE_OFF);
            userTestingScheduleEntity.setConfirm(RemindStatusConst.SCHEDULE_OFF);
            userTestingScheduleEntity.setPlus(RemindStatusConst.SCHEDULE_OFF);
        } else {
            userTestingScheduleEntity.setMax2(RemindStatusConst.SCHEDULE_OFF);
        }

        //如果什么都没有选择，那就默认max2.0
        if ((plus == null || plus == 0) && (confirm == null || confirm == 0)
                && (max == null || max == 0) && (ovum == null || ovum == 0)
                && (max2 == null || max2 == 0)) {
            if (UserGoalEnum.TTC.getValue().equals(goalStatus)||
                    UserGoalEnum.CYCLE_TRACKING.getValue().equals(goalStatus)) {
                userTestingScheduleEntity.setMax2(RemindStatusConst.SCHEDULE_ON);
            }
        }

        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, userTestingScheduleEntity);
        appUserTestingScheduleDAO.updateById(userTestingScheduleEntity);

        algorithmCallManager.editDBPeriod(loginUserInfoDTO, AlgorithmRequestTypeEnum.SET_WAND_COUNT);
    }
}
