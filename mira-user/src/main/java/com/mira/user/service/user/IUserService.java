package com.mira.user.service.user;

import com.mira.api.bluetooth.dto.period.AlgorithmEditPeriodDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.user.dto.user.*;
import com.mira.api.user.dto.user.diary.UpdatePregnantDiaryDTO;
import com.mira.api.user.dto.user.diary.UserDiaryDTO;
import com.mira.api.user.dto.user.diary.UserDiaryIntegrationDTO;
import com.mira.api.user.dto.user.diary.excel.ExportUserDiaryIntegrationDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户信息接口
 *
 * <AUTHOR>
 */
public interface IUserService {
    /**
     * 根据id获取用户信息
     *
     * @param userId 用户id
     * @return AppUserDTO
     */
    AppUserDTO getUserById(Long userId);

    /**
     * 根据邮箱获取用户信息
     *
     * @param email 邮箱
     * @return AppUserDTO
     */
    AppUserDTO getUserByEmail(String email);

    /**
     * 保存用户
     *
     * @param appUserSaveDTO 用户信息
     * @return AppUserDTO
     */
    AppUserDTO saveUser(AppUserSaveDTO appUserSaveDTO);

    /**
     * 根据id获取用户详情
     *
     * @param userId 用户id
     * @return AppUserInfoDTO
     */
    AppUserInfoDTO getUserInfoByUserId(Long userId);

    /**
     * 更新用户详情
     *
     * @param appUserInfoDTO 用户详情
     */
    void updateUserInfo(AppUserInfoDTO appUserInfoDTO);

    /**
     * 保存用户温度数据
     *
     * @param userId             用户id
     * @param userTemperatureDTO 用户温度数据
     */
    TemperatureResultDTO saveTemperature(Long userId, UserTemperatureDTO userTemperatureDTO);

    /**
     * 获取用户的 Schedule
     *
     * @param userId 用户id
     * @return TestingScheduleDTO
     */
    TestingScheduleDTO getTestingSchedule(Long userId);

    /**
     * 根据id获取partner信息
     *
     * @param partnerId partner id
     * @return AppPartnerDTO
     */
    AppPartnerDTO getPartnerById(Long partnerId);

    /**
     * 根据邮箱获取partner信息
     *
     * @param email email
     * @return AppPartnerDTO
     */
    AppPartnerDTO getPartnerByEmail(String email);

    /**
     * 获取用户日记列表
     *
     * @param userId    用户id
     * @param startDate 日期，e.g. 2022-01-01
     * @param endDate   日期，e.g. 2022-01-01
     * @return UserDList<UserDiaryDTO>iaryDTO
     */
    List<UserDiaryDTO> listUserDiary(Long userId, String startDate, String endDate);

    /**
     * 获取用户日记相关数据
     *
     * @param userId 用户id
     * @param date   日期
     * @return UserDiaryIntegrationDTO
     */
    UserDiaryIntegrationDTO getUserDiaryIntegration(Long userId, String date);

    /**
     * 获取用户日记相关数据列表
     *
     * @param userId 用户编号
     * @param dates  日期列表，e.g. 2022-01-01
     * @return Map<String, UserDiaryIntegrationDTO>
     */
    Map<String, UserDiaryIntegrationDTO> listUserDiaryIntegration(Long userId, List<String> dates);

    /**
     * 获取用户日记相关数据列表
     *
     * @param userId 用户编号
     * @return Map<String, UserDiaryIntegrationDTO>
     */
    Map<String, ExportUserDiaryIntegrationDTO> listUserAllDiaryIntegration(Long userId);

    /**
     * 获取用户经期数据
     *
     * @param userId 用户id
     * @return UserPeriodDTO
     */
    UserPeriodDTO getUserPeriod(Long userId);

    /**
     * 构建编辑经期请求数据
     *
     * @param userId                   用户id
     * @param algorithmRequestTypeEnum 算法请求类型
     * @return AlgorithmEditPeriodDTO
     */
    @Deprecated
    AlgorithmEditPeriodDTO buildAlgorithmEditPeriodDTO(Long userId, AlgorithmRequestTypeEnum algorithmRequestTypeEnum);

    /**
     * 构建编辑经期请求数据，并且打开max2.0testing schedule
     *
     * @param userId                   用户id
     * @param algorithmRequestTypeEnum 算法请求类型
     * @return AlgorithmEditPeriodDTO
     */
    AlgorithmEditPeriodDTO openMax2ScheduleAndBuildAlgorithmEditPeriodDTO(Long userId, AlgorithmRequestTypeEnum algorithmRequestTypeEnum);

    /**
     * 处理需要测试试剂可切换的用户
     *
     * @param userId   用户编号
     * @param wandType 试剂类型
     */
    void wandChange(Long userId, String wandType);

    /**
     * hcg测试怀孕结果同步到diary页面
     *
     * @param updatePregnantDiaryDTO 参数
     */
    void updatePregnantDiary(UpdatePregnantDiaryDTO updatePregnantDiaryDTO);

    /**
     * 绑定或者解绑仪器
     *
     * @param userId      用户id
     * @param userBindDTO 绑定信息
     * @return String
     */
    Integer bindUnbindDevice(Long userId, UserBindDTO userBindDTO);

    /**
     * 修改绑定的firmware版本
     *
     * @param userId             用户id
     * @param userBindVersionDTO 绑定信息
     */
    String editBindVersion(Long userId, UserBindVersionDTO userBindVersionDTO);

    /**
     * 查询邮箱列表
     */
    Map<Long, String> listEmailByIds(Set<Long> userIds);

    /**
     * 查询经期列表
     */
    List<UserPeriodDTO> listPeriodByUserIds(Set<Long> userIds);

    /**
     * 搜索用户信息 desk
     */
    List<DeskSearchUserDTO> searchDeskUserInfos(String keyword);

    /**
     * 获取用户信息 desk
     */
    DeskUserInfoDTO getDeskUserInfoDTO(Long userId);

    /**
     * 编辑用户经期 desk
     *
     * @param userId id
     */
    void deskEditPeriod(Long userId);

    /**
     * 用户paywall记录
     *
     * @param userPaywallDTO 参数
     */
    void payWall(UserPaywallDTO userPaywallDTO);

    /**
     * 修改bind-flag字段：wait-shipping
     *
     * @param userId id
     */
    void waitShipping(Long userId);
}
