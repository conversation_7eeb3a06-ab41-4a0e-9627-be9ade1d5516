package com.mira.third.party.service.provider;

import com.mira.api.thirdparty.provider.ISlackProvider;
import com.mira.core.response.CommonResult;
import com.mira.third.party.client.slack.SlackClient;
import com.mira.third.party.dto.slack.SlackChatMessageDTO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * slack 接口实现
 *
 * <AUTHOR>
 */
@RestController
public class SlackProvider implements ISlackProvider {
    @Resource
    private SlackClient slackClient;

    private final static String KEY = "Bearer *********************************************************";

    @Override
    public CommonResult<String> whiteTempAlert(String message) {
        SlackChatMessageDTO slackChatMessageDTO = new SlackChatMessageDTO();
        slackChatMessageDTO.setChannel("C08QBK1U6SE");
        slackChatMessageDTO.setText(message);
        slackClient.chatPostMessage(KEY, slackChatMessageDTO);
        return CommonResult.OK();
    }
}
